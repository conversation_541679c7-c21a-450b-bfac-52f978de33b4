import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivative_condition_order_edit/derivative_condition_order_edit_cubit.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/stop_order/widgets/editable_stop_order_fields.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/derivative_condition_order_helper.dart';

class StopOrderEditDialog extends StatelessWidget {
  const StopOrderEditDialog({super.key, this.callBack, this.model});

  final void Function(bool)? callBack;
  final ConditionOrderBookModel? model;

  @override
  Widget build(BuildContext context) {
    return _StopOrderEditDialogContent(model: model, callBack: callBack);
  }
}

class _StopOrderEditDialogContent extends StatefulWidget {
  const _StopOrderEditDialogContent({this.callBack, this.model});

  final void Function(bool)? callBack;
  final ConditionOrderBookModel? model;

  @override
  State<_StopOrderEditDialogContent> createState() =>
      _StopOrderEditDialogContentState();
}

class _StopOrderEditDialogContentState
    extends State<_StopOrderEditDialogContent> {
  late TextEditingController _volumeController;
  late TextEditingController _priceController;
  late TextEditingController _activePriceController;

  late FocusNode _volumeFocusNode;
  late FocusNode _priceFocusNode;
  late FocusNode _activePriceFocusNode;

  int volume = 0;
  double price = 0.0;
  double activePrice = 0.0;

  @override
  void initState() {
    super.initState();

    // Initialize values from model using helper
    volume = widget.model?.qty?.toInt() ?? 0;
    price = DerivativeConditionOrderHelper.convertPriceFromServer(
      widget.model?.price is String
          ? double.tryParse(widget.model?.price ?? '0')
          : widget.model?.price as num?,
    );
    activePrice = DerivativeConditionOrderHelper.convertPriceFromServer(
      widget.model?.activePrice,
    );

    // Initialize controllers
    _volumeController = TextEditingController(text: volume.toString());
    _priceController = TextEditingController(text: price.toStringAsFixed(1));
    _activePriceController = TextEditingController(
      text: activePrice.toStringAsFixed(1),
    );

    // Initialize focus nodes
    _volumeFocusNode = FocusNode();
    _priceFocusNode = FocusNode();
    _activePriceFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _volumeController.dispose();
    _priceController.dispose();
    _activePriceController.dispose();
    _volumeFocusNode.dispose();
    _priceFocusNode.dispose();
    _activePriceFocusNode.dispose();
    super.dispose();
  }

  void _updateVolume(int newVolume) {
    if (newVolume >= 0) {
      setState(() {
        volume = newVolume;
        _volumeController.text = newVolume.toString();
        _volumeController.selection = TextSelection.fromPosition(
          TextPosition(offset: _volumeController.text.length),
        );
      });
    }
  }

  void _updatePrice(double newPrice) {
    if (newPrice >= 0) {
      setState(() {
        price = newPrice;
        _priceController.text = newPrice.toStringAsFixed(1);
        _priceController.selection = TextSelection.fromPosition(
          TextPosition(offset: _priceController.text.length),
        );
      });
    }
  }

  void _updateActivePrice(double newActivePrice) {
    if (newActivePrice >= 0) {
      setState(() {
        activePrice = newActivePrice;
        _activePriceController.text = newActivePrice.toStringAsFixed(1);
        _activePriceController.selection = TextSelection.fromPosition(
          TextPosition(offset: _activePriceController.text.length),
        );
      });
    }
  }

  void _handleConfirm() {
    // Validate inputs
    final validationError = DerivativeConditionOrderHelper.validateOrderInputs(
      volume: volume,
      price: price,
      activePrice: activePrice,
    );

    if (validationError != null) {
      showSnackBar(context, validationError, isSuccess: false);
      return;
    }

    // Create the condition order request using helper
    final request = DerivativeConditionOrderHelper.createStopOrderRequest(
      originalOrder: widget.model!,
      newVolume: volume,
      newPrice: price,
      newActivePrice: activePrice,
    );

    // Call the API
    context
        .read<DerivativeConditionOrderEditCubit>()
        .editDerivativeConditionOrder(request: request);
  }

  void _handleCancel() {
    context.pop();
  }

  @override
  Widget build(BuildContext context) {
    return  Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            Text(
              'Sửa lệnh',
              style: context.textStyle.headineBold6?.copyWith(
                color: context.colors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Information section
            _buildInformationSection(context),
            const SizedBox(height: 16),

            // Input fields section
            _buildInputFieldsSection(context),
            const SizedBox(height: 16),

            // Action buttons
            _buildActionButtons(context),
          ],
        );
       
     
  }

  Widget _buildInformationSection(BuildContext context) {
    return Column(
      children: [
        // Order Type (read-only)
        _buildInfoRow(context, label: 'Loại lệnh', value: 'Stop Order'),
        const SizedBox(height: 8),

        // Contract Code (read-only)
        _buildInfoRow(
          context,
          label: 'Mã hợp đồng',
          value: widget.model?.symbol ?? '-',
        ),
      ],
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Row(
      children: [
        Expanded(
          child: Text(
            label,
            style: context.textStyle.body14?.copyWith(
              color: context.colors.textSecondary,
            ),
          ),
        ),
        Text(
          value,
          style: context.textStyle.subtitle14?.copyWith(
            color: valueColor ?? context.colors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildInputFieldsSection(BuildContext context) {
    return EditableStopOrderFields(
      volumeController: _volumeController,
      priceController: _priceController,
      activePriceController: _activePriceController,
      onVolumeChanged: _updateVolume,
      onPriceChanged: _updatePrice,
      onActivePriceChanged: _updateActivePrice,
      volumeFocusNode: _volumeFocusNode,
      priceFocusNode: _priceFocusNode,
      activePriceFocusNode: _activePriceFocusNode,
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
     
  ) {
    final isLoading =
         context.watch<DerivativeConditionOrderEditCubit>().state.status == DerivativeConditionOrderEditStatus.loading;
    return Row(
      children: [
        Expanded(
          child: VpsButton.secondaryXsSmall(
            title: 'Đóng',
            onPressed: isLoading ? null : _handleCancel,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: VpsButton.primaryXsSmall(
            title: 'Xác nhận',
            onPressed: isLoading ? null : _handleConfirm,
          ),
        ),
      ],
    );
  }
}
