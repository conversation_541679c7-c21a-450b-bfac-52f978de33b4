import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/animation/blink.dart';
import 'package:vp_trading/cubit/derivative/validate_order/derivative_validate_order_cubit.dart';
import 'package:vp_trading/generated/assets.gen.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/screen/place_order/awaiting/widget/choice_equal_buy_button.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_box.dart';
import 'package:vp_trading/screen/place_order/widgets/input_view/custom/input_field_error.dart';
import 'package:vp_trading/utils/text_inputformater.dart';

class EditableStopOrderFields extends StatefulWidget {
  final TextEditingController volumeController;
  final TextEditingController priceController;
  final TextEditingController activePriceController;
  final Function(int) onVolumeChanged;
  final Function(double) onPriceChanged;
  final Function(double) onActivePriceChanged;
  final FocusNode? volumeFocusNode;
  final FocusNode? priceFocusNode;
  final FocusNode? activePriceFocusNode;

  const EditableStopOrderFields({
    super.key,
    required this.volumeController,
    required this.priceController,
    required this.activePriceController,
    required this.onVolumeChanged,
    required this.onPriceChanged,
    required this.onActivePriceChanged,
    this.volumeFocusNode,
    this.priceFocusNode,
    this.activePriceFocusNode,
  });

  @override
  State<EditableStopOrderFields> createState() =>
      _EditableStopOrderFieldsState();
}

class _EditableStopOrderFieldsState extends State<EditableStopOrderFields> {
  final ValueNotifier<bool> _priceBlink = ValueNotifier(false);
  final ValueNotifier<bool> _activePriceBlink = ValueNotifier(false);

  @override
  void dispose() {
    _priceBlink.dispose();
    _activePriceBlink.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<
          DerivativeValidateOrderCubit,
          DerivativeValidateOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.currentPrice != current.currentPrice,
          listener: (context, state) {
            if (state.currentPrice != null) {
              widget.priceController.text = state.currentPrice!;
              widget.priceController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.priceController.text.length),
              );
            }
          },
        ),
        BlocListener<
          DerivativeValidateOrderCubit,
          DerivativeValidateOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.currentActivePrice != current.currentActivePrice,
          listener: (context, state) {
            if (state.currentActivePrice != null) {
              widget.activePriceController.text = state.currentActivePrice!;
              widget
                  .activePriceController
                  .selection = TextSelection.fromPosition(
                TextPosition(offset: widget.activePriceController.text.length),
              );
            }
          },
        ),
        BlocListener<
          DerivativeValidateOrderCubit,
          DerivativeValidateOrderState
        >(
          listenWhen:
              (previous, current) =>
                  previous.currentVolume != current.currentVolume,
          listener: (context, state) {
            if (state.currentVolume != null) {
              widget.volumeController.text = state.currentVolume!;
              widget.volumeController.selection = TextSelection.fromPosition(
                TextPosition(offset: widget.volumeController.text.length),
              );
            }
          },
        ),
      ],
      child: Column(
        children: [
          _buildVolumeField(context),
          const SizedBox(height: 16),
          _buildPriceField(context),
          const SizedBox(height: 16),
          _buildActivePriceField(context),
        ],
      ),
    );
  }

  Widget _buildVolumeField(BuildContext context) {
    return BlocBuilder<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState
    >(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Khối lượng đặt',
                    style: context.textStyle.body14?.copyWith(
                      color: vpColor.textSecondary,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                SizedBox(
                  width: 137,
                  child: InputFieldBox(
                    controller: widget.volumeController,
                    hintText: 'KL',
                    isError:
                        state.errorVolume.isEditOrderError &&
                        widget.volumeController.text.isNotEmpty,
                    onChange: (value) {
                      context
                          .read<DerivativeValidateOrderCubit>()
                          .onChangeVolume(value);
                      final newVolume =
                          int.tryParse(value.replaceAll(',', '')) ?? 0;
                      widget.onVolumeChanged(newVolume);
                    },
                    focusNode: widget.volumeFocusNode,
                    onTap: (increase) {
                      context.read<DerivativeValidateOrderCubit>().volumeTap(
                        text: widget.volumeController.text,
                        increase: increase,
                      );
                    },
                    inputFormatters: [
                      removeZeroStartInputFormatter,
                      ...volumeInputFormatter,
                    ],
                  ),
                ),
              ],
            ),
            InputFieldError(
              errorMessage: state.errorVolume.message(null),
              text: widget.volumeController.text,
              isShake: true,
            ),
          ],
        );
      },
    );
  }

  Widget _buildPriceField(BuildContext context) {
    return BlocBuilder<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState
    >(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Giá đặt',
                    style: context.textStyle.body14?.copyWith(
                      color: vpColor.textSecondary,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                SizedBox(
                  width: 137,
                  child: ColoredBox(
                    color: vpColor.backgroundElevation0,
                    child: Blink(
                      blink: _priceBlink,
                      child: InputFieldBox(
                        controller: widget.priceController,
                        hintText: 'Giá',
                        isError:
                            state.errorPrice.isErrorEditOrder &&
                            widget.priceController.text.isNotEmpty,
                        sessionValue: state.sessionType?.name.toUpperCase(),
                        onChange: (value) {
                          context
                              .read<DerivativeValidateOrderCubit>()
                              .onChangePrice(value);
                          final newPrice =
                              double.tryParse(value.replaceAll(',', '')) ?? 0.0;
                          widget.onPriceChanged(newPrice);
                        },
                        focusNode: widget.priceFocusNode,
                        onTap: (increase) {
                          context.read<DerivativeValidateOrderCubit>().priceTap(
                            text: widget.priceController.text,
                            increase: increase,
                          );
                        },
                        inputFormatters: [
                          removeZeroStartInputFormatter,
                          LengthLimitingTextInputFormatter(8),
                          ...priceInputFormatter,
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            InputFieldError(
              errorMessage: state.errorPrice.message,
              text: widget.priceController.text,
              isShake: true,
            ),
          ],
        );
      },
    );
  }

  Widget _buildActivePriceField(BuildContext context) {
    return BlocBuilder<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState
    >(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Điều kiện kích hoạt',
                  style: context.textStyle.body14?.copyWith(
                    color: vpColor.textPrimary,
                  ),
                ),
                const SizedBox(width: 16),
                const _ButtonLessThan(),
                const SizedBox(width: 4),
                const _ButtonGreaterThan(),
                const SizedBox(width: 4),
                Expanded(
                  child: ColoredBox(
                    color: vpColor.backgroundElevation0,
                    child: Blink(
                      blink: _activePriceBlink,
                      child: InputFieldBox(
                        controller: widget.activePriceController,
                        hintText: 'Giá',
                        isError:
                            state.errorActivePrice.isErrorEditOrder &&
                            widget.activePriceController.text.isNotEmpty,
                        sessionValue: state.sessionType?.name.toUpperCase(),
                        onChange: (value) {
                          context
                              .read<DerivativeValidateOrderCubit>()
                              .onChangeActivePrice(value);
                          final newActivePrice =
                              double.tryParse(value.replaceAll(',', '')) ?? 0.0;
                          widget.onActivePriceChanged(newActivePrice);
                        },
                        focusNode: widget.activePriceFocusNode,
                        onTap: (increase) {
                          context.read<DerivativeValidateOrderCubit>().priceTap(
                            text: widget.activePriceController.text,
                            increase: increase,
                            activation: true,
                          );
                        },
                        inputFormatters: [
                          removeZeroStartInputFormatter,
                          LengthLimitingTextInputFormatter(8),
                          ...priceInputFormatter,
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            InputFieldError(
              errorMessage: state.errorActivePrice.message,
              text: widget.activePriceController.text,
              isShake: true,
            ),
          ],
        );
      },
    );
  }
}

class _ButtonGreaterThan extends StatelessWidget {
  const _ButtonGreaterThan();

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState,
      ActivationConditionsType?
    >(
      selector: (state) => state.activationType,
      builder: (_, activationType) {
        return ChoiceEqualButton(
          assetsIcon: VpTradingAssets.icons.icGreaterThanEqual.path,
          isFocus: activationType == ActivationConditionsType.greaterThan,
          onTap:
              () => context
                  .read<DerivativeValidateOrderCubit>()
                  .setActivationConditions(
                    ActivationConditionsType.greaterThan,
                  ),
        );
      },
    );
  }
}

class _ButtonLessThan extends StatelessWidget {
  const _ButtonLessThan();

  @override
  Widget build(BuildContext context) {
    return BlocSelector<
      DerivativeValidateOrderCubit,
      DerivativeValidateOrderState,
      ActivationConditionsType?
    >(
      selector: (state) => state.activationType,
      builder: (_, activationType) {
        return ChoiceEqualButton(
          assetsIcon: VpTradingAssets.icons.icLessThanEqual.path,
          isFocus: activationType == ActivationConditionsType.lessThan,
          onTap:
              () => context
                  .read<DerivativeValidateOrderCubit>()
                  .setActivationConditions(ActivationConditionsType.lessThan),
        );
      },
    );
  }
}
