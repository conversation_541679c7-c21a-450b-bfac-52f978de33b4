import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/vp_design_system.dart';
import 'package:vp_trading/cubit/derivative_condition_order_edit/derivative_condition_order_edit_cubit.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/edit_order_conditional_widget.dart';

void showEditConditionalOrderDialog({
  required BuildContext context,
  void Function()? onConfirm,
  void Function(bool)? callBack,
  void Function()? onCancel,
  ConditionOrderBookModel? model,
}) {
  VPPopup.custom(
    padding: const EdgeInsets.all(20),
    child: BlocProvider(
      create: (context) => DerivativeConditionOrderEditCubit(),
      child: BlocConsumer<
        DerivativeConditionOrderEditCubit,
        DerivativeConditionOrderEditState
      >(
        listener: (context, state) {
          if (state.status == DerivativeConditionOrderEditStatus.success) {
            showSnackBar(context, 'S<PERSON>a lệnh thành công', isSuccess: true);

            callBack?.call(true);
            context.pop();
          } else if (state.status ==
              DerivativeConditionOrderEditStatus.failure) {
            showSnackBar(
              context,
              state.errorMessage ?? 'Có lỗi xảy ra khi sửa lệnh',
              isSuccess: false,
            );
          }
        },
        builder: (context, state) {
          return EditOrderConditionalDialog(model: model);
        },
      ),
    ),
  ).showDialog(context);
}
