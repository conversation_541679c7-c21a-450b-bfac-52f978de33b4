import 'package:vp_common/utils/app_helper.dart';
import 'package:vp_trading/model/order/condition_order_book/condition_order_book_model.dart';
import 'package:vp_trading/model/order/request/condition_order_request_model.dart';

/// Helper class để tạo request cho các loại lệnh điều kiện phái sinh
class DerivativeConditionOrderHelper {
  /// Parse price from String to num
  static num? _parsePrice(String? priceString) {
    if (priceString == null || priceString.isEmpty) return null;
    return double.tryParse(priceString);
  }

  /// Tạo request cho Stop Order
  static ConditionOrderRequestModel createStopOrderRequest({
    required ConditionOrderBookModel originalOrder,
    required int newVolume,
    required double newPrice,
    required double newActivePrice,
  }) {
    return ConditionOrderRequestModel(
      requestId: "app_${AppHelper().genXRequestID()}",
      orderType: originalOrder.orderType ?? '',
      accountId: originalOrder.accountId ?? '',
      orderId: originalOrder.orderId ?? '',
      conditionInfo: ConditionInfo(
        symbol: originalOrder.symbol ?? '',
        qty: newVolume,
        side: originalOrder.side?.toLowerCase() ?? '',
        type: "limit",
        price: newPrice * 1000, // Convert to server format
        fromDate: originalOrder.fromDate ?? '',
        toDate: originalOrder.toDate ?? '',
        activePrice: newActivePrice * 1000, // Convert to server format
        activeType: originalOrder.activeType ?? '',
      ),
    );
  }

  /// Tạo request cho Take Profit/Stop Loss
  static ConditionOrderRequestModel createTakeProfitStopLossRequest({
    required ConditionOrderBookModel originalOrder,
    required int newVolume,
    double? newPriceTP,
    double? newPriceSL,
    double? newActivePriceTP,
    double? newActivePriceSL,
  }) {
    return ConditionOrderRequestModel(
      requestId: "app_${AppHelper().genXRequestID()}",
      orderType: originalOrder.orderType ?? '',
      accountId: originalOrder.accountId ?? '',
      orderId: originalOrder.orderId ?? '',
      conditionInfo: ConditionInfo(
        symbol: originalOrder.symbol ?? '',
        qty: newVolume,
        side: originalOrder.side?.toLowerCase() ?? '',
        type: "limit",
        fromDate: originalOrder.fromDate ?? '',
        toDate: originalOrder.toDate ?? '',
        // Take Profit fields
        priceTP:
            newPriceTP != null
                ? newPriceTP * 1000
                : _parsePrice(originalOrder.priceTP),
        activepriceTP:
            newActivePriceTP != null
                ? newActivePriceTP * 1000
                : originalOrder.activepriceTP,
        priceTypeTP: originalOrder.priceTypeTP,
        // Stop Loss fields
        priceSL:
            newPriceSL != null
                ? newPriceSL * 1000
                : _parsePrice(originalOrder.priceSL),
        activepriceSL:
            newActivePriceSL != null
                ? newActivePriceSL * 1000
                : originalOrder.activepriceSL,
        priceTypeSL: originalOrder.priceTypeSL,
        // Other fields
        timetype: originalOrder.timeTypeValue,
        split: originalOrder.split,
        priceStep: _parsePrice(originalOrder.priceStep),
        deltaValue: originalOrder.deltaValue,
        deltaType: originalOrder.deltaType,
      ),
    );
  }

  /// Tạo request cho Trailing Stop Order
  static ConditionOrderRequestModel createTrailingStopOrderRequest({
    required ConditionOrderBookModel originalOrder,
    required int newVolume,
    required double newActivePrice,
    double? newDeltaValue,
    String? newDeltaType,
  }) {
    return ConditionOrderRequestModel(
      requestId: "app_${AppHelper().genXRequestID()}",
      orderType: originalOrder.orderType ?? '',
      accountId: originalOrder.accountId ?? '',
      orderId: originalOrder.orderId ?? '',
      conditionInfo: ConditionInfo(
        symbol: originalOrder.symbol ?? '',
        qty: newVolume,
        side: originalOrder.side?.toLowerCase() ?? '',
        type: "limit",
        fromDate: originalOrder.fromDate ?? '',
        toDate: originalOrder.toDate ?? '',
        activePrice: newActivePrice * 1000, // Convert to server format
        activeType: originalOrder.activeType ?? '',
        deltaValue: newDeltaValue ?? originalOrder.deltaValue,
        deltaType: newDeltaType ?? originalOrder.deltaType,
        priceStep: _parsePrice(originalOrder.priceStep),
      ),
    );
  }

  /// Tạo request chung cho TPSL
  static ConditionOrderRequestModel createTPSLRequest({
    required ConditionOrderBookModel originalOrder,
    required int newVolume,
    double? newPriceTP,
    double? newPriceSL,
    double? newActivePriceTP,
    double? newActivePriceSL,
    String? newPriceTypeTP,
    String? newPriceTypeSL,
  }) {
    return ConditionOrderRequestModel(
      requestId: "app_${AppHelper().genXRequestID()}",
      orderType: originalOrder.orderType ?? '',
      accountId: originalOrder.accountId ?? '',
      orderId: originalOrder.orderId ?? '',
      conditionInfo: ConditionInfo(
        symbol: originalOrder.symbol ?? '',
        qty: newVolume,
        side: originalOrder.side?.toLowerCase() ?? '',
        type: "limit",
        fromDate: originalOrder.fromDate ?? '',
        toDate: originalOrder.toDate ?? '',
        // Take Profit fields
        priceTP:
            newPriceTP != null
                ? newPriceTP * 1000
                : _parsePrice(originalOrder.priceTP),
        activepriceTP:
            newActivePriceTP != null
                ? newActivePriceTP * 1000
                : originalOrder.activepriceTP,
        priceTypeTP: newPriceTypeTP ?? originalOrder.priceTypeTP,
        // Stop Loss fields
        priceSL:
            newPriceSL != null
                ? newPriceSL * 1000
                : _parsePrice(originalOrder.priceSL),
        activepriceSL:
            newActivePriceSL != null
                ? newActivePriceSL * 1000
                : originalOrder.activepriceSL,
        priceTypeSL: newPriceTypeSL ?? originalOrder.priceTypeSL,
        // Other fields
        timetype: originalOrder.timeTypeValue,
        split: originalOrder.split,
        priceStep: _parsePrice(originalOrder.priceStep),
        deltaValue: originalOrder.deltaValue,
        deltaType: originalOrder.deltaType,
      ),
    );
  }

  /// Chuyển đổi giá từ server format (chia cho 1000) sang display format
  static double convertPriceFromServer(num? serverPrice) {
    if (serverPrice == null) return 0.0;
    return serverPrice.toDouble() / 1000;
  }

  /// Chuyển đổi giá từ display format sang server format (nhân với 1000)
  static double convertPriceToServer(double displayPrice) {
    return displayPrice * 1000;
  }

  /// Validate input values
  static String? validateOrderInputs({
    required int volume,
    required double price,
    required double activePrice,
  }) {
    if (volume <= 0) {
      return 'Khối lượng phải lớn hơn 0';
    }

    if (price <= 0) {
      return 'Giá đặt phải lớn hơn 0';
    }

    if (activePrice <= 0) {
      return 'Giá kích hoạt phải lớn hơn 0';
    }

    return null; // No errors
  }
}
