part of 'derivative_condition_order_edit_cubit.dart';

enum DerivativeConditionOrderEditStatus { initial, loading, success, failure }

class DerivativeConditionOrderEditState extends Equatable {
  const DerivativeConditionOrderEditState({
    this.status = DerivativeConditionOrderEditStatus.initial,
    this.errorMessage,
  });

  final DerivativeConditionOrderEditStatus status;

  final String? errorMessage;

  bool get isLoading => status == DerivativeConditionOrderEditStatus.loading;

  bool get isSuccess => status == DerivativeConditionOrderEditStatus.success;

  bool get isFailure => status == DerivativeConditionOrderEditStatus.failure;

  bool get canPerformAction => !isLoading;

  @override
  List<Object?> get props => [status, errorMessage];

  DerivativeConditionOrderEditState copyWith({
    DerivativeConditionOrderEditStatus? status,
    String? errorMessage,
  }) {
    return DerivativeConditionOrderEditState(status: status ?? this.status);
  }
}
