import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:vp_common/vp_common.dart';

extension StringExt on String {
  double get volume {
    String moneyValue = this;
    if (contains(',')) {
      moneyValue = replaceAll(',', '');
    }
    if (contains('.')) {
      moneyValue = replaceAll('.', '');
    }

    return double.tryParse(moneyValue) ?? 0.0;
  }

  double? get price {
    String priceValue = this;
    if (contains(',')) {
      priceValue = replaceAll(',', '');
    }
    final price = double.tryParse(priceValue);
    if (price == null) {
      return null;
    }
    return (price * 1000).toPrecision(0);
  }

  double? get priceDerivative {
    String priceValue = this;
    if (contains(',')) {
      priceValue = replaceAll(',', '');
    }
    final price = double.tryParse(priceValue);
    if (price == null) {
      return null;
    }
    return price;
  }
}

extension NullableStringExt on String? {
  bool get isETF => this == '3';

  bool get isCW => this == '4';
}

extension MapExt<k, v> on Map<k, v> {
  List<T> toList<T>(T Function(MapEntry<k, v> entry) convert) =>
      entries.map(convert).toList();
}

extension MapStringBoolExt on Map<String, bool> {
  String mergeText(String Function(String) getText, {String separate = ' '}) {
    String label = '';
    forEach((text, isKeyLang) {
      label += isKeyLang ? getText(text) : text;
      if (keys.last != text) {
        label += separate;
      }
    });
    return label;
  }
}

extension NumExt on num {
  double get stepHose =>
      this < 10000.0
          ? 10.0
          : this >= 10000.0 && this < 50000.0
          ? 50.0
          : 100.0;

  String get valueText {
    String text = toString();
    text = text.replaceAll(RegExp(r'\.\d*'), '');
    text = text.replaceAll(RegExp(r'\D'), '');
    text = text.replaceAll(RegExp(r'\B(?=(\d{3})+(?!\d))'), ',');
    return text + ' đ';
  }

  String get volumeString {
    final format = NumberFormat("#,##0", "en_US");

    String result = format.format(this);
    result = result.trim().replaceAll('.', ',');

    return result;
  }
}

extension NavigatorStateExtension on NavigatorState {
  void pushIfNotExist(String newRouteName, {Object? arguments}) {
    bool isNewRouteSameAsCurrent = false;

    Navigator.popUntil(context, (route) {
      if (route.settings.name == newRouteName) {
        isNewRouteSameAsCurrent = true;
      }
      return true;
    });

    if (!isNewRouteSameAsCurrent) {
      pushNamed(newRouteName, arguments: arguments);
    }
  }

  void popUntilOrPushIfNotExist(
    String routeName, {
    required String routeRoot,
    Object? arguments,
  }) {
    if (!_isCurrent(routeName, routeRoot)) {
      pushNamed(routeName, arguments: arguments);
    }
  }

  bool _isCurrent(String routeName, String routeRoot) {
    bool isCurrent = false;
    popUntil((route) {
      if (route.settings.name == routeName) {
        isCurrent = true;
        return true;
      }
      if (route.settings.name == routeRoot) {
        return true;
      }
      return false;
    });
    return isCurrent;
  }

  void popUntilCount({required int count}) {
    int currentCount = 0;
    popUntil((_) => ++currentCount > count);
  }

  void popUntilSafety(String routeName) {
    Navigator.popUntil(context, (route) => getRoutePredicate(route, routeName));
  }

  bool getRoutePredicate(Route<dynamic> route, String routeName) {
    if (route.isFirst) return true;

    return !route.willHandlePopInternally &&
        route is ModalRoute &&
        route.settings.name == routeName;
  }

  Future? pushNamedAndRemoveUntilSafety(
    String newRouteName,
    String predicateRouteName, {
    Object? arguments,
  }) {
    return Navigator.of(context).pushNamedAndRemoveUntil(
      newRouteName,
      (route) => getRoutePredicate(route, predicateRouteName),
      arguments: arguments,
    );
  }
}
